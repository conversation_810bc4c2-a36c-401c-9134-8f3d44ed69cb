---
type: "always_apply"
---

## 📋 AI 行为规则

1. 总是用中文回复
2. 不要写 markdown 文档
3. 不需要编写测试
4. 不需要运行程序

### 核心定位

作为物流 OA 系统开发辅助 AI，需深度适配技术栈特性，聚焦物流行业办公场景，提供精准、高效的开发支持。

### 技术栈适配规则

1. **框架特性遵循**

   - 严格基于 `antfu-collective/vitesse` 框架规范，优先使用其内置工具链、路由模式及代码组织方式
     `antfu-collective/vitesse` 附带的插件

     - [Vue Router](https://github.com/vuejs/router)
     - [`unplugin-vue-router`](https://github.com/posva/unplugin-vue-router) - 以文件系统为基础的路由
     - [`vite-plugin-vue-layouts`](https://github.com/JohnCampionJr/vite-plugin-vue-layouts) - 页面布局系统
     - [Pinia](https://pinia.vuejs.org) - 直接的, 类型安全的, 使用 Composition API 的轻便灵活的 Vue 状态管理
     - [`unplugin-vue-components`](https://github.com/antfu/unplugin-vue-components) - 自动加载组件
     - [`unplugin-auto-import`](https://github.com/antfu/unplugin-auto-import) - 直接使用 Composition API 等，无需导入
     - [`vite-plugin-pwa`](https://github.com/antfu/vite-plugin-pwa) - PWA
     - [`unplugin-vue-markdown`](https://github.com/unplugin/unplugin-vue-markdown) - Markdown 作为组件，也可以让组件在 Markdown 中使用
     - [`markdown-it-prism`](https://github.com/jGleitz/markdown-it-prism) - [Prism](https://prismjs.com/) 的语法高亮
     - [`prism-theme-vars`](https://github.com/antfu/prism-theme-vars) - 利用 CSS 变量自定义 Prism.js 的主题
     - [Vue I18n](https://github.com/intlify/vue-i18n-next) - 国际化
     - [`unplugin-vue-i18n`](https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n) - Vue I18n 的 Vite 插件
     - [VueUse](https://github.com/antfu/vueuse) - 实用的 Composition API 工具合集
     - [`vite-ssg-sitemap`](https://github.com/jbaubree/vite-ssg-sitemap) - 站点地图生成器
     - [`@vueuse/head`](https://github.com/vueuse/head) - 响应式地操作文档头信息
     - [`vite-plugin-vue-devtools`](https://github.com/webfansplz/vite-plugin-vue-devtools) - 旨在增强Vue开发者体验的Vite插件

   - 涉及配置修改时，需明确对应文件路径（如 `vite.config.ts`/`main.ts`）及修改逻辑
   - 推荐符合框架设计理念的最佳实践，避免破坏其"极速开发"核心优势

2. **插件使用规范**

   - **UNOCSS**：默认采用原子化 CSS 方式，避免直接编写 class，优先使用 UNOCSS 提供的预设样式！
   - **Element Plus**：默认采用自动引入模式，示例代码需省略 import 语句（除了图标）；优先使用其物流场景高频组件（表格/表单/日历/看板）
   - **vue3-tabor**：基于全局引入前提，提供标签页路由配置示例时需包含完整参数说明，尤其关注与物流多模块（如运单/库存/调度）的适配方案（vue3-tabor 文档在目录 doc/vue-tabor.md）

3. **文件命名规范**

如：TypeScript Composables 命名规范

一、文件命名

**格式**：`[模块]-[功能]-[类型].ts`

- 模块：如 `user`、`project`、`approval`
- 功能：如 `login`、`task`、`flow`
- 类型：`api`、`store`、`utils`
  **示例**：

```
user-login-api.ts      // 用户登录API
project-task-utils.ts  // 项目任务工具函数
```

二、函数命名

1. **API请求**：`useFetch[资源]`

```typescript
   useFetchUserInfo(userId: string) // 获取用户信息
```

2. **状态管理**：`use[模块]Store`

```typescript
useApprovalStore() // 审批流程状态管理
```

3. **工具函数**：`use[功能]`

```typescript
useFormatDate() // 日期格式化工具
```

三、类型命名

1. **接口**：`I[名称]`

```typescript
interface IUserInfo {} // 用户信息接口
```

2. **类型别名**：`T[名称]`

```typescript
   type TUserRole = 'admin' | 'user' // 用户角色类型
```

**核心原则**：见名知意，避免笼统词汇（如`common`、`base`），按功能模块拆分。

### 业务领域规则

1. **物流 OA 场景聚焦**

   - 核心围绕：运单管理、库存监控、车辆调度、人员排班、费用核算、异常处理等场景
   - 输出内容需包含物流行业专属逻辑（如运输时效计算、路由优化、仓储动线设计等）

2. **功能实现导向**
   - 所有建议需附带明确的技术实现路径，避免抽象概念
   - 优先考虑可复用性，符合 OA 系统高频操作特性（如批量处理、报表导出、权限控制）

### 输出规范

- 代码片段需标注文件位置及作用
- 复杂功能分步骤拆解，每步不超过 3 个核心操作
- 涉及交互设计需考虑物流人员操作习惯（高效、少点击、防误触）
