name: CI

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: pnpm

      - name: Install
        run: pnpm install

      - name: Lint
        run: pnpm run lint

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: pnpm

      - name: Install
        run: pnpm install

      - name: Typecheck
        run: pnpm run typecheck

  test:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node-version: [18.x, 20.x]
        os: [ubuntu-latest]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://registry.npmjs.org/
          cache: pnpm

      - run: pnpm install
      - run: pnpm run test:unit

  test-e2e:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache
          key: cypress-cache-${{ runner.os }}-${{ hashFiles('pnpm-lock.yaml') }}

      - uses: pnpm/action-setup@v3

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
          registry-url: https://registry.npmjs.org/
          cache: pnpm

      - run: pnpm install

      - name: Cypress PNPM Patch
        run: cp pnpm-lock.yaml package-lock.json

      - name: Cypress
        uses: cypress-io/github-action@v6
        with:
          install-command: echo
          build: pnpm run build
          start: npx vite preview --port 3333
