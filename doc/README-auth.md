# 认证系统使用说明

## 概述

本系统提供了完整的用户认证功能，包括：
- 用户登录/退出
- Token 自动刷新
- 路由认证守卫
- 权限检查

## 核心组件

### 1. API 客户端 (`src/composables/api-client.ts`)

通用的 RESTful API 客户端，支持：
- GET、POST、PUT、PATCH、DELETE 请求
- 文件上传和下载
- 自动添加认证头
- 请求超时控制
- 全局错误处理

```typescript
// 使用示例
const { get, post, put, delete: del } = useApiClient()

// GET 请求
const users = await get('/api/users', { page: 1, size: 10 })

// POST 请求
const result = await post('/api/users', { name: '张三', email: '<EMAIL>' })

// 跳过认证的请求
const publicData = await get('/api/public', {}, { skipAuth: true })
```

### 2. 用户存储 (`src/stores/user.ts`)

管理用户状态和认证相关功能：

```typescript
const userStore = useUserStore()

// 用户登录
await userStore.login('admin', 'password')

// 检查登录状态
if (userStore.isLoggedIn) {
  console.log('用户已登录:', userStore.userInfo)
}

// 检查权限
if (userStore.hasPermission('user:create')) {
  // 有创建用户权限
}

// 检查角色
if (userStore.hasRole('admin')) {
  // 是管理员角色
}

// 手动刷新 Token
const success = await userStore.refreshAccessToken()

// 检查 Token 有效性
const isValid = await userStore.checkTokenValid()

// 用户退出
await userStore.logout()
```

### 3. 认证守卫 (`src/modules/auth-guard.ts`)

自动处理路由认证：
- 未登录用户访问需要认证的页面时自动跳转到登录页
- 已登录用户访问登录页时自动跳转到首页
- 自动检查和刷新 Token

## API 接口说明

### 登录接口

```bash
POST /v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin"
}
```

响应格式：
```typescript
{
  code: 1,  // 0为失败 1为成功
  message: "登录成功",
  data: {
    id: 1,
    username: "admin",
    token: "eyJhbGciOiJIUzI1NiIs...",
    refreshToken: "refresh_token_here",
    expiresIn: 3600,
    roleName: "管理员",
    // ... 其他用户信息
  }
}
```

### 退出登录接口

```bash
POST /v1/auth/logout
Authorization: Bearer {token}
```

### 刷新 Token 接口（预留）

```bash
POST /v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh_token_here"
}
```

### Token 检查接口（预留）

```bash
GET /v1/auth/check-token
Authorization: Bearer {token}
```

## 登录成功事件系统

系统使用 Pinia 状态管理来处理登录成功事件，替代了传统的 `window.dispatchEvent` 方式。

### 基本用法

```typescript
// 在组件中监听登录成功事件
const userStore = useUserStore()

// 方式1: 直接监听状态变化
watch(() => userStore.loginSuccessTimestamp, (newTimestamp, oldTimestamp) => {
  if (newTimestamp > 0 && newTimestamp !== oldTimestamp) {
    console.log('用户登录成功!')
    // 执行登录成功后的逻辑
  }
})

// 方式2: 使用专用的 Composable
const { onLoginSuccess, onLoginSuccessOnce } = useLoginSuccessWatcher()

// 监听登录成功（可多次触发）
onLoginSuccess(() => {
  console.log('用户登录成功!')
  // 执行相关逻辑
})

// 监听登录成功（只触发一次）
onLoginSuccessOnce(() => {
  console.log('用户首次登录成功!')
  // 执行一次性逻辑
})
```

### 在布局组件中的应用

```vue
<script setup lang="ts">
const userStore = useUserStore()
const tabor = useTabor()

// 监听登录成功，清理登录页标签
watch(() => userStore.loginSuccessTimestamp, (newTimestamp, oldTimestamp) => {
  if (newTimestamp > 0 && newTimestamp !== oldTimestamp) {
    // 清理登录页面标签
    setTimeout(() => {
      if (tabor && typeof tabor.close === 'function') {
        tabor.close({ fullPath: '/auth/login' })
      }
    }, 100)
  }
})
</script>
```

## 使用场景

### 1. 页面组件中使用

```vue
<script setup lang="ts">
const userStore = useUserStore()

// 检查用户是否有权限访问某功能
const canEdit = computed(() => userStore.hasPermission('user:edit'))

// 获取当前用户信息
const currentUser = computed(() => userStore.userInfo)
</script>

<template>
  <div>
    <h1>欢迎，{{ currentUser?.nickname }}</h1>
    <button v-if="canEdit" @click="editUser">编辑用户</button>
  </div>
</template>
```

### 2. API 调用

```typescript
// 在 composable 或组件中
const { get, post } = useApiClient()

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await get('/api/users')
    if (response.code === 1) {
      return response.data
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  }
}

// 创建用户
const createUser = async (userData: any) => {
  try {
    const response = await post('/api/users', userData)
    if (response.code === 1) {
      ElMessage.success('用户创建成功')
      return response.data
    }
  } catch (error) {
    ElMessage.error('用户创建失败')
  }
}
```

### 3. 路由守卫配置

在页面组件中设置布局来启用认证：

```vue
<script setup lang="ts">
// 使用 oa 布局会自动启用认证检查
defineOptions({
  name: 'UserManagePage',
})
</script>

<template>
  <div>
    <!-- 页面内容 -->
  </div>
</template>

<route lang="yaml">
meta:
  layout: oa  # 使用 oa 布局，会自动进行认证检查
</route>
```

## 注意事项

1. **Token 自动刷新**：目前为预留功能，默认返回成功。实际使用时需要根据后端 API 实现。

2. **权限系统**：当前使用简单的字符串匹配，实际项目中可能需要更复杂的权限控制逻辑。

3. **错误处理**：API 客户端会自动处理 401 错误（未授权），自动跳转到登录页。

4. **本地存储**：用户信息和 Token 会保存在 localStorage 中，页面刷新后会自动恢复登录状态。

5. **开发环境**：可以通过环境变量 `VITE_API_LOG_ENABLED=true` 启用 API 请求日志。
