# 全局事件总线使用说明

## 概述

基于 Pinia 实现的全局事件总线系统，类似 `uni.$emit` 的功能，提供了类型安全、响应式的事件通信机制。

## 核心特性

- 🚀 **基于 Pinia**：利用 Pinia 的响应式特性
- 🔒 **类型安全**：完整的 TypeScript 支持
- 🎯 **一次性监听**：支持只执行一次的监听器
- 📦 **自动清理**：组件卸载时自动清理监听器
- 💾 **事件缓存**：支持立即执行（如果有缓存数据）
- 🛠️ **调试友好**：提供丰富的调试工具

## 基本使用

### 1. 基础事件总线

```typescript
// 在组件中使用
const { $emit, $on, $once, $off } = useEventBus()

// 发送事件
$emit('user:login', { userId: '123', username: 'admin' })

// 监听事件
$on('user:login', (data) => {
  console.log('用户登录:', data)
})

// 监听事件（只执行一次）
$once('user:logout', (data) => {
  console.log('用户退出:', data)
})

// 取消监听
$off('user:login', listener)
```

### 2. 自动清理的事件总线

```typescript
// 组件卸载时自动清理所有监听器
const { $emit, $on, $once } = useEventBusWithCleanup()

// 这些监听器会在组件卸载时自动清理
$on('data:refresh', (data) => {
  console.log('数据刷新:', data)
})

$once('task:completed', (data) => {
  console.log('任务完成:', data)
})
```

### 3. 类型安全的事件总线

```typescript
// 使用预定义的事件类型
const { $emit, $on } = useTypedEventBus()

// 类型安全的事件发送和监听
$emit('user:login', { userId: '123', username: 'admin' }) // ✅ 类型正确
$emit('user:login', { invalid: 'data' }) // ❌ 类型错误

$on('user:login', (data) => {
  // data 的类型是 { userId: string; username: string }
  console.log(data.userId, data.username)
})
```

## 预定义事件

系统提供了常用的事件名称常量：

```typescript
import { EventNames } from '~/composables/use-event-bus'

// 用户相关
EventNames.USER_LOGIN           // 'user:login'
EventNames.USER_LOGOUT          // 'user:logout'
EventNames.USER_INFO_UPDATED    // 'user:info-updated'

// 系统相关
EventNames.THEME_CHANGED        // 'system:theme-changed'
EventNames.LANGUAGE_CHANGED     // 'system:language-changed'

// 物流相关
EventNames.ORDER_CREATED        // 'logistics:order-created'
EventNames.ORDER_UPDATED        // 'logistics:order-updated'
EventNames.SHIPMENT_STATUS_CHANGED // 'logistics:shipment-status-changed'

// 表单相关
EventNames.FORM_SUBMITTED       // 'form:submitted'
EventNames.FORM_VALIDATED       // 'form:validated'
```

## 高级功能

### 1. 立即执行

```typescript
const { $on } = useEventBus()

// 如果事件之前已经发送过，立即执行监听器
$on('user:login', (data) => {
  console.log('用户信息:', data)
}, { immediate: true })
```

### 2. 一次性监听

```typescript
const { $on, $once } = useEventBus()

// 方式1：使用 $once
$once('task:completed', (data) => {
  console.log('任务完成:', data)
})

// 方式2：使用配置选项
$on('task:completed', (data) => {
  console.log('任务完成:', data)
}, { once: true })
```

### 3. 手动清理

```typescript
const { $on, $off } = useEventBus()

// 保存清理函数
const cleanup = $on('data:refresh', (data) => {
  console.log('数据刷新:', data)
})

// 手动清理
cleanup()

// 或者使用 $off
$off('data:refresh', listener)
```

## 实际应用场景

### 1. 跨组件通信

```vue
<!-- 组件A：发送事件 -->
<script setup lang="ts">
const { $emit } = useEventBus()

function handleOrderCreate() {
  $emit('logistics:order-created', {
    orderId: 'ORD001',
    orderData: { /* 订单数据 */ }
  })
}
</script>

<!-- 组件B：监听事件 -->
<script setup lang="ts">
const { $on } = useEventBusWithCleanup()

$on('logistics:order-created', (data) => {
  console.log('新订单创建:', data.orderId)
  // 刷新订单列表
  refreshOrderList()
})
</script>
```

### 2. 系统级事件

```typescript
// 主题切换
const { $emit, $on } = useEventBus()

// 发送主题切换事件
$emit('system:theme-changed', { theme: 'dark' })

// 监听主题切换
$on('system:theme-changed', ({ theme }) => {
  document.documentElement.setAttribute('data-theme', theme)
})
```

### 3. 表单验证

```typescript
// 表单组件
const { $emit } = useEventBus()

function validateForm() {
  const isValid = /* 验证逻辑 */
  $emit('form:validated', {
    formId: 'user-form',
    isValid,
    errors: isValid ? undefined : errors
  })
}

// 父组件
const { $on } = useEventBusWithCleanup()

$on('form:validated', ({ formId, isValid, errors }) => {
  if (isValid) {
    console.log('表单验证通过')
  } else {
    console.log('表单验证失败:', errors)
  }
})
```

## 调试工具

```typescript
const { 
  $getListenerCount, 
  $getEventNames, 
  $hasListeners 
} = useEventBus()

// 获取事件的监听器数量
console.log($getListenerCount('user:login')) // 2

// 获取所有事件名称
console.log($getEventNames()) // ['user:login', 'data:refresh', ...]

// 检查是否有监听器
console.log($hasListeners('user:login')) // true
```

## 最佳实践

### 1. 事件命名规范

```typescript
// 推荐：使用模块:动作的格式
'user:login'
'order:created'
'system:theme-changed'

// 不推荐：过于简单或模糊
'login'
'update'
'change'
```

### 2. 使用类型安全版本

```typescript
// 推荐：使用类型安全的事件总线
const { $emit, $on } = useTypedEventBus()

// 不推荐：使用无类型版本（除非是动态事件名）
const { $emit, $on } = useEventBus()
```

### 3. 组件中使用自动清理版本

```vue
<script setup lang="ts">
// 推荐：在组件中使用自动清理版本
const { $on } = useEventBusWithCleanup()

// 不推荐：手动管理清理（除非有特殊需求）
const { $on, $off } = useEventBus()
</script>
```

### 4. 避免事件滥用

```typescript
// 推荐：用于跨组件、跨模块通信
$emit('order:status-changed', { orderId, status })

// 不推荐：用于父子组件通信（应该使用 props/emit）
// 父子组件应该使用 Vue 的原生通信方式
```

## 注意事项

1. **内存泄漏**：在组件中使用时，建议使用 `useEventBusWithCleanup()` 避免内存泄漏
2. **事件命名**：使用清晰的命名规范，避免事件名冲突
3. **类型安全**：尽量使用 `useTypedEventBus()` 获得更好的开发体验
4. **调试**：可以通过调试工具查看当前的事件监听情况
5. **性能**：避免在高频事件中使用，如 scroll、resize 等
