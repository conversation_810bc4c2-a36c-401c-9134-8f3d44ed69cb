{"type": "module", "private": true, "packageManager": "pnpm@10.7.0", "scripts": {"build": "vite-ssg build", "dev": "vite --port 3333 --open", "lint": "eslint .", "preview": "vite preview", "preview-https": "serve dist", "test": "vitest", "test:e2e": "cypress open", "test:unit": "vitest", "typecheck": "vue-tsc --noEmit", "up": "taze major -I", "postinstall": "npx simple-git-hooks", "sizecheck": "npx vite-bundle-visualizer"}, "dependencies": {"@unhead/vue": "catalog:frontend", "@unocss/reset": "catalog:frontend", "@vueuse/core": "catalog:frontend", "element-plus": "^2.10.4", "nprogress": "catalog:frontend", "pinia": "catalog:frontend", "vue": "catalog:frontend", "vue-i18n": "catalog:frontend", "vue-router": "catalog:frontend", "vue3-tabor": "^0.2.2"}, "devDependencies": {"@antfu/eslint-config": "catalog:dev", "@iconify-json/carbon": "catalog:dev", "@intlify/unplugin-vue-i18n": "catalog:build", "@shikijs/markdown-it": "catalog:build", "@types/markdown-it-link-attributes": "catalog:types", "@types/nprogress": "catalog:types", "@unocss/eslint-config": "catalog:build", "@vitejs/plugin-vue": "catalog:build", "@vue-macros/volar": "catalog:dev", "@vue/test-utils": "catalog:dev", "beasties": "catalog:build", "cypress": "catalog:dev", "cypress-vite": "catalog:dev", "eslint": "catalog:dev", "eslint-plugin-cypress": "catalog:dev", "eslint-plugin-format": "catalog:dev", "https-localhost": "catalog:dev", "lint-staged": "catalog:dev", "markdown-it-link-attributes": "catalog:build", "rollup": "catalog:build", "shiki": "catalog:build", "simple-git-hooks": "catalog:dev", "taze": "catalog:dev", "typescript": "catalog:dev", "unocss": "catalog:build", "unplugin-auto-import": "catalog:build", "unplugin-vue-components": "catalog:build", "unplugin-vue-macros": "catalog:build", "unplugin-vue-markdown": "catalog:build", "unplugin-vue-router": "catalog:build", "vite": "catalog:build", "vite-bundle-visualizer": "catalog:build", "vite-plugin-inspect": "catalog:build", "vite-plugin-pwa": "catalog:build", "vite-plugin-vue-devtools": "catalog:build", "vite-plugin-vue-layouts": "catalog:build", "vite-ssg": "catalog:build", "vite-ssg-sitemap": "catalog:build", "vitest": "catalog:dev", "vue-tsc": "catalog:dev"}, "resolutions": {"unplugin": "catalog:build", "vite": "catalog:build", "vite-plugin-inspect": "catalog:build"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}