import type { IApiResponse } from '../types/api'

/**
 * API 请求配置接口
 */
export interface IApiRequestConfig extends RequestInit {
  params?: Record<string, any>
  timeout?: number
  skipAuth?: boolean
  skipErrorHandler?: boolean
}

/**
 * API 客户端类
 */
class ApiClient {
  private baseURL: string
  private defaultTimeout: number
  private defaultHeaders: Record<string, string>

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8080'
    this.defaultTimeout = Number(import.meta.env.VITE_API_TIMEOUT) || 10000
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }
  }

  /**
   * 构建完整的 URL
   */
  private buildUrl(url: string, params?: Record<string, any>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`

    if (!params || Object.keys(params).length === 0) {
      return fullUrl
    }

    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, String(item)))
        }
        else {
          searchParams.append(key, String(value))
        }
      }
    })

    const separator = fullUrl.includes('?') ? '&' : '?'
    return `${fullUrl}${separator}${searchParams.toString()}`
  }

  /**
   * 获取请求头
   */
  private getHeaders(config: IApiRequestConfig = {}): Record<string, string> {
    const headers: any = { ...this.defaultHeaders, ...config.headers }

    // 添加认证头
    if (!config.skipAuth) {
      const userStore = useUserStore()
      if (userStore.token) {
        headers.Authorization = `Bearer ${userStore.token}`
      }
    }

    return headers
  }

  /**
   * 处理响应
   */
  private async handleResponse<T>(response: Response): Promise<IApiResponse<T>> {
    if (!response.ok) {
      // 处理 HTTP 错误状态
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`

      try {
        const errorData = await response.json()
        if (errorData.message) {
          errorMessage = errorData.message
        }
      }
      catch {
        // 如果解析错误响应失败，使用默认错误信息
      }

      throw new Error(errorMessage)
    }

    const data = await response.json()
    return data
  }

  /**
   * 发送请求
   */
  private async request<T = any>(
    url: string,
    config: IApiRequestConfig = {},
  ): Promise<IApiResponse<T>> {
    const {
      params,
      timeout = this.defaultTimeout,
      skipAuth = false,
      skipErrorHandler = false,
      ...requestInit
    } = config

    const fullUrl = this.buildUrl(url, params)
    const headers = this.getHeaders(config)

    try {
      // 创建 AbortController 用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      const response = await fetch(fullUrl, {
        ...requestInit,
        headers,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      const result = await this.handleResponse<T>(response)

      // 记录请求日志（开发环境）
      if (import.meta.env.VITE_API_LOG_ENABLED === 'true') {
        console.warn(`[API] ${requestInit.method || 'GET'} ${url}:`, {
          request: {
            url: fullUrl,
            params,
            body: requestInit.body ? JSON.parse(requestInit.body as string) : null,
          },
          response: result,
        })
      }

      return result
    }
    catch (error: any) {
      // 处理网络错误
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接')
      }

      console.error(`[API Error] ${requestInit.method || 'GET'} ${url}:`, error)

      // 如果不跳过错误处理，可以在这里添加全局错误处理逻辑
      if (!skipErrorHandler) {
        // 例如：token 过期处理、网络错误提示等
        this.handleGlobalError(error)
      }

      throw error
    }
  }

  /**
   * 全局错误处理
   */
  private handleGlobalError(error: any) {
    // 可以在这里添加全局错误处理逻辑
    // 例如：token 过期自动跳转登录页、显示错误提示等
    if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
      // Token 过期处理
      const userStore = useUserStore()
      userStore.logout()

      // 跳转到登录页
      const router = useRouter()
      router.push('/auth/login')

      ElMessage.error('登录已过期，请重新登录')
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'GET' })
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    return this.request<T>(url, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    return this.request<T>(url, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    return this.request<T>(url, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    return this.request<T>(url, { ...config, method: 'DELETE' })
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, file: File | FormData, config?: IApiRequestConfig): Promise<IApiResponse<T>> {
    const formData = file instanceof FormData ? file : new FormData()
    if (file instanceof File) {
      formData.append('file', file)
    }

    return this.request<T>(url, {
      ...config,
      method: 'POST',
      body: formData,
      headers: {
        // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
        ...config?.headers,
        'Content-Type': undefined,
      } as any,
    })
  }

  /**
   * 下载文件
   */
  async download(url: string, filename?: string, config?: IApiRequestConfig): Promise<void> {
    const response = await fetch(this.buildUrl(url, config?.params), {
      ...config,
      method: 'GET',
      headers: this.getHeaders(config),
    })

    if (!response.ok) {
      throw new Error(`下载失败: ${response.statusText}`)
    }

    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}

// 创建全局 API 客户端实例
const apiClient = new ApiClient()

/**
 * API 客户端 Composable
 * 提供 RESTful API 的多种请求方式
 */
export function useApiClient() {
  return {
    get: apiClient.get.bind(apiClient),
    post: apiClient.post.bind(apiClient),
    put: apiClient.put.bind(apiClient),
    patch: apiClient.patch.bind(apiClient),
    delete: apiClient.delete.bind(apiClient),
    upload: apiClient.upload.bind(apiClient),
    download: apiClient.download.bind(apiClient),
  }
}

// 导出 API 客户端实例供其他模块使用
export { apiClient }
