import { useTabor } from 'vue3-tabor'

/**
 * Tabor 通用工具函数
 */
export function useTaborUtils() {
  const tabor = useTabor()

  /**
   * 清除所有标签页
   * 通常在用户登录/退出时使用，确保标签页状态重置
   */
  function clearAllTabs() {
    try {
      // 获取当前所有标签页并关闭（除了首页）
      // 注意：vue3-tabor 可能没有直接的 clearAll 方法
      // 我们使用 closeOthers 来关闭除当前页面外的所有标签页
      tabor.closeOthers()

      // 如果当前不在首页，也关闭当前页面
      const currentRoute = useRoute()
      if (currentRoute.path !== '/dashboard') {
        tabor.close()
      }
    } catch (error) {
      console.warn('清除标签页失败:', error)
    }
  }

  /**
   * 登录时清除所有标签页（保留首页）
   */
  function clearAllTabsOnLogin() {
    try {
      // 关闭除首页外的所有标签页
      tabor.closeOthers('/dashboard')
    } catch (error) {
      console.warn('登录时清除标签页失败:', error)
    }
  }

  /**
   * 退出登录时清除所有标签页
   */
  function clearAllTabsOnLogout() {
    try {
      // 关闭所有标签页
      tabor.closeOthers()

      // 如果当前不在登录页，也关闭当前标签页
      const currentRoute = useRoute()
      if (currentRoute.path !== '/auth/login') {
        tabor.close()
      }
    } catch (error) {
      console.warn('退出登录时清除标签页失败:', error)
    }
  }

  /**
   * 关闭所有标签页并跳转到指定页面
   * @param targetPath 目标路径，默认为首页
   */
  function clearAllTabsAndGoTo(targetPath: string = '/dashboard') {
    try {
      const router = useRouter()
      
      // 先清除所有标签页
      clearAllTabs()
      
      // 延迟跳转，确保标签页清理完成
      setTimeout(() => {
        router.push(targetPath)
      }, 100)
    } catch (error) {
      console.warn('清除标签页并跳转失败:', error)
    }
  }

  /**
   * 检查指定路径的标签页是否存在
   * @param path 路径
   */
  function hasTab(path: string): boolean {
    try {
      return tabor.has(path)
    } catch (error) {
      console.warn('检查标签页存在性失败:', error)
      return false
    }
  }

  /**
   * 查找指定路径的标签页
   * @param path 路径
   */
  function findTab(path: string) {
    try {
      return tabor.find(path)
    } catch (error) {
      console.warn('查找标签页失败:', error)
      return null
    }
  }

  /**
   * 安全地打开标签页
   * @param to 路由信息
   * @param options 选项
   */
  function safeOpenTab(to: any, options?: any) {
    try {
      return tabor.open(to, options)
    } catch (error) {
      console.warn('打开标签页失败:', error)
      // 降级处理：直接使用路由跳转
      const router = useRouter()
      if (typeof to === 'string') {
        router.push(to)
      } else if (to.path) {
        router.push(to)
      }
    }
  }

  /**
   * 安全地关闭标签页
   * @param item 标签页标识
   * @param toOptions 跳转选项
   */
  function safeCloseTab(item?: any, toOptions?: any) {
    try {
      return tabor.close(item, toOptions)
    } catch (error) {
      console.warn('关闭标签页失败:', error)
    }
  }

  /**
   * 刷新指定标签页
   * @param tabId 标签页ID，不传则刷新当前页面
   */
  function refreshTab(tabId?: string) {
    try {
      tabor.refresh(tabId)
    } catch (error) {
      console.warn('刷新标签页失败:', error)
      // 降级处理：刷新整个页面
      if (!tabId) {
        window.location.reload()
      }
    }
  }

  return {
    clearAllTabs,
    clearAllTabsOnLogin,
    clearAllTabsOnLogout,
    clearAllTabsAndGoTo,
    hasTab,
    findTab,
    safeOpenTab,
    safeCloseTab,
    refreshTab,
  }
}
