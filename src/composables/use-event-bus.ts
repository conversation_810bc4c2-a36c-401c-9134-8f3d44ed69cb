/**
 * 全局事件总线 Composable
 * 类似 uni.$emit 的全局消息系统
 */
export function useEventBus() {
  const eventBusStore = useEventBusStore()

  return {
    /**
     * 发送事件
     * @param eventName 事件名称
     * @param data 事件数据
     */
    $emit: eventBusStore.emit,

    /**
     * 监听事件
     * @param eventName 事件名称
     * @param listener 监听器函数
     * @param config 配置选项
     */
    $on: eventBusStore.on,

    /**
     * 监听事件（只执行一次）
     * @param eventName 事件名称
     * @param listener 监听器函数
     * @param immediate 是否立即执行
     */
    $once: eventBusStore.once,

    /**
     * 取消监听事件
     * @param eventName 事件名称
     * @param listener 监听器函数
     */
    $off: eventBusStore.off,

    /**
     * 取消所有事件监听
     */
    $clear: eventBusStore.clear,

    /**
     * 获取事件的监听器数量
     * @param eventName 事件名称
     */
    $getListenerCount: eventBusStore.getListenerCount,

    /**
     * 获取所有事件名称
     */
    $getEventNames: eventBusStore.getEventNames,

    /**
     * 检查是否有监听器
     * @param eventName 事件名称
     */
    $hasListeners: eventBusStore.hasListeners,
  }
}

/**
 * 在组件中自动管理事件监听器的 Composable
 * 组件卸载时自动清理监听器
 */
export function useEventBusWithCleanup() {
  const eventBus = useEventBus()
  const cleanupFunctions = ref<Array<() => void>>([])

  /**
   * 监听事件（组件卸载时自动清理）
   * @param eventName 事件名称
   * @param listener 监听器函数
   * @param config 配置选项
   */
  function $on<T = any>(
    eventName: string,
    listener: (data: T) => void,
    config?: { once?: boolean, immediate?: boolean },
  ) {
    const cleanup = eventBus.$on(eventName, listener, config)
    cleanupFunctions.value.push(cleanup)
    return cleanup
  }

  /**
   * 监听事件（只执行一次，组件卸载时自动清理）
   * @param eventName 事件名称
   * @param listener 监听器函数
   * @param immediate 是否立即执行
   */
  function $once<T = any>(
    eventName: string,
    listener: (data: T) => void,
    immediate = false,
  ) {
    const cleanup = eventBus.$once(eventName, listener, immediate)
    cleanupFunctions.value.push(cleanup)
    return cleanup
  }

  /**
   * 清理所有监听器
   */
  function cleanup() {
    cleanupFunctions.value.forEach(fn => fn())
    cleanupFunctions.value = []
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 发送事件（无需清理）
    $emit: eventBus.$emit,

    // 监听事件（自动清理）
    $on,
    $once,

    // 手动清理
    cleanup,

    // 其他工具方法
    $off: eventBus.$off,
    $clear: eventBus.$clear,
    $getListenerCount: eventBus.$getListenerCount,
    $getEventNames: eventBus.$getEventNames,
    $hasListeners: eventBus.$hasListeners,
  }
}

/**
 * 预定义的常用事件名称
 */
export const EventNames = {
  // 用户相关
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_INFO_UPDATED: 'user:info-updated',

  // 系统相关
  THEME_CHANGED: 'system:theme-changed',
  LANGUAGE_CHANGED: 'system:language-changed',
  WINDOW_RESIZE: 'system:window-resize',

  // 业务相关
  DATA_REFRESH: 'business:data-refresh',
  NOTIFICATION_RECEIVED: 'business:notification-received',
  TASK_COMPLETED: 'business:task-completed',

  // 物流相关
  ORDER_CREATED: 'logistics:order-created',
  ORDER_UPDATED: 'logistics:order-updated',
  ORDER_CANCELLED: 'logistics:order-cancelled',
  SHIPMENT_STATUS_CHANGED: 'logistics:shipment-status-changed',
  INVENTORY_UPDATED: 'logistics:inventory-updated',
  VEHICLE_STATUS_CHANGED: 'logistics:vehicle-status-changed',

  // 表单相关
  FORM_SUBMITTED: 'form:submitted',
  FORM_VALIDATED: 'form:validated',
  FORM_RESET: 'form:reset',

  // 弹窗相关
  MODAL_OPENED: 'modal:opened',
  MODAL_CLOSED: 'modal:closed',
  DRAWER_OPENED: 'drawer:opened',
  DRAWER_CLOSED: 'drawer:closed',
} as const

/**
 * 事件数据类型定义
 */
export interface EventDataTypes {
  [EventNames.USER_LOGIN]: { userId: string, username: string }
  [EventNames.USER_LOGOUT]: { userId: string }
  [EventNames.USER_INFO_UPDATED]: { userId: string, changes: Record<string, any> }
  [EventNames.THEME_CHANGED]: { theme: 'light' | 'dark' }
  [EventNames.LANGUAGE_CHANGED]: { language: string }
  [EventNames.WINDOW_RESIZE]: { width: number, height: number }
  [EventNames.DATA_REFRESH]: { module: string, timestamp: number }
  [EventNames.NOTIFICATION_RECEIVED]: { id: string, title: string, content: string }
  [EventNames.TASK_COMPLETED]: { taskId: string, result: any }
  [EventNames.ORDER_CREATED]: { orderId: string, orderData: any }
  [EventNames.ORDER_UPDATED]: { orderId: string, changes: any }
  [EventNames.ORDER_CANCELLED]: { orderId: string, reason: string }
  [EventNames.SHIPMENT_STATUS_CHANGED]: { shipmentId: string, status: string }
  [EventNames.INVENTORY_UPDATED]: { itemId: string, quantity: number }
  [EventNames.VEHICLE_STATUS_CHANGED]: { vehicleId: string, status: string }
  [EventNames.FORM_SUBMITTED]: { formId: string, data: any }
  [EventNames.FORM_VALIDATED]: { formId: string, isValid: boolean, errors?: any }
  [EventNames.FORM_RESET]: { formId: string }
  [EventNames.MODAL_OPENED]: { modalId: string, props?: any }
  [EventNames.MODAL_CLOSED]: { modalId: string, result?: any }
  [EventNames.DRAWER_OPENED]: { drawerId: string, props?: any }
  [EventNames.DRAWER_CLOSED]: { drawerId: string, result?: any }
}

/**
 * 类型安全的事件总线 Composable
 */
export function useTypedEventBus() {
  const eventBus = useEventBus()

  return {
    /**
     * 发送类型安全的事件
     */
    $emit: <K extends keyof EventDataTypes>(
      eventName: K,
      data: EventDataTypes[K],
    ) => eventBus.$emit(eventName, data),

    /**
     * 监听类型安全的事件
     */
    $on: <K extends keyof EventDataTypes>(
      eventName: K,
      listener: (data: EventDataTypes[K]) => void,
      config?: { once?: boolean, immediate?: boolean },
    ) => eventBus.$on(eventName, listener, config),

    /**
     * 监听类型安全的事件（只执行一次）
     */
    $once: <K extends keyof EventDataTypes>(
      eventName: K,
      listener: (data: EventDataTypes[K]) => void,
      immediate?: boolean,
    ) => eventBus.$once(eventName, listener, immediate),

    // 其他方法保持不变
    $off: eventBus.$off,
    $clear: eventBus.$clear,
    $getListenerCount: eventBus.$getListenerCount,
    $getEventNames: eventBus.$getEventNames,
    $hasListeners: eventBus.$hasListeners,
  }
}
