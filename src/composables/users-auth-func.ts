import type { ILoginRequest, ILoginResponse } from '~/types/auth'
import type { IReturnResponse } from '~/types/app'


export class Users {
  /**
   * 用户登录函数
   * @param params 登录参数
   * @returns Promise<boolean> 登录是否成功
   */
  async useLogin(params: ILoginRequest): Promise<IReturnResponse> {
    try {
      // 直接调用登录接口
      const response = await apiClient.post<ILoginResponse>('/v1/auth/login', params, {
        skipAuth: true,
      })

      if (response.code !== 1 || !response.data) {
        throw new Error(response.message || '登录失败')
      }

      // 保存登录信息到本地存储
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))
      
      return {
        code: 1,
        message: '登录成功',
        data: response.data.userInfo,
      }
    }
    catch (error: any) {
      return {
        code: 0,
        message: error.message || '登录失败，请检查用户名和密码',
      }
    }
  }
}
