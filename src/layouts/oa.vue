<script setup lang="ts">
import { ArrowDown, Document, Expand, Fold, Folder, House, Moon, Setting, Sunny, SwitchButton, User } from '@element-plus/icons-vue'
import { markRaw, ref } from 'vue'

import { useTabor } from 'vue3-tabor'

const tabor = useTabor()

// 路由
const router = useRouter()
const route = useRoute()

// 判断是否有 app_initialized
const isFirst = localStorage.getItem('app_initialized') === 'true'
if (isFirst) {
  // 移除 app_initialized
  localStorage.removeItem('app_initialized')
  // 首次加载 移除东西专用
  // console.warn('关闭其他标签页')
  // 检查登录页是否存在，存在则移除
  const routerPaths = ['/auth/login']
  routerPaths.forEach((path) => {
    if (tabor.has(path)) {
      tabor.close({ fullPath: path })
    }
  })
  tabor.closeOthers()
}

// 菜单项类型定义
interface MenuItem {
  id: string
  title: string
  icon: any
  path?: string
  src?: string
  children?: MenuItem[]
}

// 菜单数据
const menuList = ref<MenuItem[]>([
  // 默认是欢迎页 dashboard
  {
    id: 'home',
    title: '首页',
    icon: markRaw(House),
    path: '/dashboard',
  },
  {
    id: 'test',
    title: '测试管理',
    icon: markRaw(Document),
    children: [
      {
        id: 'tabor-test',
        title: 'Tabor测试',
        icon: markRaw(Folder),
        path: '/test/tabor-test',
      },
      {
        // 状态测试 event-bus-demo.vue
        id: 'event-bus-demo',
        title: '事件总线测试',
        icon: markRaw(Folder),
        path: '/test/event-bus-demo',
      },
      {
        id: '2-1',
        title: '示例页面',
        icon: markRaw(Folder),
        src: 'https://home.shichengguoji.com/start.html',
      },
      {
        id: '2-3',
        title: 'Element Plus',
        icon: markRaw(Folder),
        src: 'https://element-plus.org/zh-CN/',
      },
    ],
  },

])

const isCollapse = ref(false)
function toggleCollapse() {
  isCollapse.value = !isCollapse.value
}

// 处理外部页面菜单点击
function handleExternalMenuClick(menu: MenuItem) {
  // 打开外部页面
  openURL(menu)
}

// 处理普通路由菜单点击
function handleRouteMenuClick(menu: MenuItem) {
  if (menu.path) {
    // 使用tabor打开路由页面
    tabor.open({ path: menu.path })
  }
}

// 打开网页
function openURL(menu: MenuItem) {
  if (!menu.src) {
    ElMessage.error('缺少页面地址')
    return
  }

  try {
    // 验证 URL 格式
    const isValidUrl = new URL(menu.src)
    if (!isValidUrl)
      throw new Error('Invalid URL')

    // 生成唯一的时间戳作为标识符，确保每个外部页面都有独立的标签页
    const timestamp = Date.now()

    // 打开iframe标签
    tabor.open({
      path: '/iframe',
      query: {
        src: menu.src,
        title: menu.title,
        t: timestamp, // 添加时间戳参数确保路径唯一性
      },
    }, {
      tabConfig: {
        name: menu.title || '外部页面',
        keepAlive: false, // iframe 页面通常不需要缓存
        // 使用完整路径作为key，确保每个外部页面都有独立的标签页
        key: 'fullPath',
        iframeAttributes: {
          src: menu.src,
          width: '100%',
          height: '100%',
        },
      },
    })
  }
  catch (error) {
    ElMessage.error('无效的页面地址')
    console.error('Invalid URL:', menu.src, error)
  }
}

// 用户下拉菜单处理
function handleUserCommand(command: string) {
  switch (command) {
    case 'profile':
      ElMessage.info('跳转到个人中心')
      // 可以在这里添加跳转到个人中心的逻辑
      // tabor.open({ path: '/profile' })
      break
    case 'settings':
      ElMessage.info('跳转到系统设置')
      // 可以在这里添加跳转到系统设置的逻辑
      // tabor.open({ path: '/settings' })
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
async function handleLogout() {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 先清理标签页
    tabor.closeOthers()
    // 判断当前页面 是否非主页
    if (route.path !== '/dashboard') {
      tabor.close()
    }

    ElMessage.success('退出登录成功')

    // 移除所有本地存储
    localStorage.clear()
    // 刷新页面
    window.location.reload()
  }
  catch (error: any) {
    // 用户取消或退出失败
    if (error !== 'cancel') {
      console.error('Logout error:', error)
      ElMessage.error('退出登录失败')
    }
  }
}
</script>

<template>
  <el-container class="h-screen overflow-hidden">
    <!-- 顶部导航 -->
    <el-header h="14" flex="~ items-center justify-between" border="b" px="4">
      <div flex="~ items-center">
        <el-text text="xl" font="bold" :class="{ 'ml-16': isCollapse }">
          OA System
        </el-text>
      </div>
      <div flex="~ items-center">
        <!-- 黑暗模式切换按钮 -->
        <el-button
          text circle :icon="isDark ? markRaw(Moon) : markRaw(Sunny)" :title="isDark ? '切换到亮色模式' : '切换到暗色模式'"
          mr="2" @click="toggleDark()"
        />

        <!-- 用户信息下拉菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div
            flex="~ items-center" cursor="pointer" px="2" py="1" rounded hover="bg-gray-100 dark:bg-gray-700"
            transition="all duration-200"
          >
            <el-avatar size="small" mr="2">
              A
            </el-avatar>
            <el-text mr="2">
              {{ '用户' }}
            </el-text>
            <el-icon size="12" ml-2>
              <ArrowDown />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon mr="1">
                  <User />
                </el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon mr="1">
                  <Setting />
                </el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon mr="1">
                  <SwitchButton />
                </el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主体内容区 -->
    <el-container flex="1" class="overflow-hidden">
      <!-- 左侧菜单 -->
      <el-aside
        :style="{ width: isCollapse ? '64px' : '200px' }" border="r" bg="gray-50 dark:gray-800"
        transition="width duration-300"
      >
        <!-- 动态菜单结构 -->
        <el-menu :default-active="$route.path" class="h-full !border-none" :collapse="isCollapse" unique-opened>
          <!-- 折叠按钮 -->
          <div text="center" cursor="pointer" p="2" border="b" @click="toggleCollapse">
            <el-icon>
              <component :is="isCollapse ? markRaw(Expand) : markRaw(Fold)" />
            </el-icon>
          </div>
          <template v-for="menu in menuList" :key="menu.id">
            <!-- 有子菜单时显示为 sub-menu -->
            <el-sub-menu v-if="menu.children" :index="menu.id">
              <template #title>
                <el-icon>
                  <component :is="menu.icon" />
                </el-icon>
                <span>{{ menu.title }}</span>
              </template>
              <template v-for="subMenu in menu.children" :key="subMenu.id">
                <!-- 外部页面菜单项 -->
                <el-menu-item v-if="subMenu.src" :index="subMenu.id" @click="handleExternalMenuClick(subMenu)">
                  <el-icon>
                    <component :is="subMenu.icon" />
                  </el-icon>
                  <span>{{ subMenu.title }}</span>
                </el-menu-item>
                <!-- 普通路由菜单项 -->
                <el-menu-item v-else-if="subMenu.path" :index="subMenu.path" @click="handleRouteMenuClick(subMenu)">
                  <el-icon>
                    <component :is="subMenu.icon" />
                  </el-icon>
                  <span>{{ subMenu.title }}</span>
                </el-menu-item>
              </template>
            </el-sub-menu>
            <!-- 外部页面顶级菜单项 -->
            <el-menu-item v-else-if="menu.src" :index="menu.id" @click="handleExternalMenuClick(menu)">
              <el-icon>
                <component :is="menu.icon" />
              </el-icon>
              <span>{{ menu.title }}</span>
            </el-menu-item>
            <!-- 普通路由顶级菜单项 -->
            <el-menu-item v-else-if="menu.path" :index="menu.path" @click="handleRouteMenuClick(menu)">
              <el-icon>
                <component :is="menu.icon" />
              </el-icon>
              <span>{{ menu.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 右侧内容区 -->
      <el-main flex="~ 1 col" class="!m-0 !p-0" style="background-color: var(--el-bg-color-page);">
        <!-- 标签页导航 -->
        <vue-tabor page-class="main h-full !m-0 !p-0 overflow-auto" />
      </el-main>
    </el-container>
  </el-container>
</template>

<style></style>
