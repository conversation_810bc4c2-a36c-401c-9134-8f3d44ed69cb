import type { UserModule } from './types'

import { setupLayouts } from 'virtual:generated-layouts'
import { ViteSSG } from 'vite-ssg'

import Tabor from 'vue3-tabor'
import { routes } from 'vue-router/auto-routes'
import App from './App.vue'
// 必须引入样式文件
import 'vue3-tabor/dist/assets/index.css'

import '@unocss/reset/tailwind.css'
import './styles/main.css'
import 'uno.css'

// console.log(routes)

// https://github.com/antfu/vite-ssg
export const createApp = ViteSSG(
  App,
  {
    routes: setupLayouts(routes),
    base: import.meta.env.BASE_URL,
  },
  (ctx) => {
    // 先注册 vue3-tabor
    ctx.app.use(Tabor, {
      router: ctx.router, // 传入router实例
      maxCache: 10, // 可选：最大缓存数量
    })

    // 然后安装所有模块
    Object.values(import.meta.glob<{ install: UserModule }>('./modules/*.ts', { eager: true }))
      .forEach(i => i.install?.(ctx))
    // ctx.app.use(Previewer)
  },
)
