import type { UserModule } from '~/types'

/**
 * 认证路由守卫模块
 * 确保未登录用户无法访问需要认证的页面
 */
export const install: UserModule = ({ router }) => {
  // 不需要认证的路由路径
  const publicRoutes = [
    '/auth/login',
    '/auth/register',
    '/auth/forgot-password',
    '/404',
    '/[...all]',
  ]

  // 需要认证的布局
  const authRequiredLayouts = ['oa', 'default']

  router.beforeEach(async (to, from, next) => {
    // 检查是否是公开路由
    const isPublicRoute = publicRoutes.some((route) => {
      if (route.includes('[...all]')) {
        return to.path.startsWith('/404') || to.matched.length === 0
      }
      return to.path === route || to.path.startsWith(route)
    })

    let isLoggedIn = true

    // 检查是否需要认证的布局
    const requiresAuth = to.meta?.layout && authRequiredLayouts.includes(to.meta.layout as string)

    // 如果是公开路由，直接通过
    if (isPublicRoute) {
      // 如果已登录用户访问登录页，重定向到首页
      if (to.path === '/auth/login' && isLoggedIn) {
        next('/dashboard')
        return
      }
      next()
      return
    }

    // 如果需要认证但用户未登录，重定向到登录页
    if (requiresAuth && !isLoggedIn) {
      ElMessage.warning('请先登录')
      next({
        path: '/auth/login',
        query: { redirect: to.fullPath }, // 保存原始路径，登录后可以重定向回来
      })
      return
    }

    // 如果用户已登录且访问需要认证的页面，检查 token 有效性
    if (requiresAuth && isLoggedIn) {
      try {
        // 尝试自动刷新 token（如果需要的话）
        const tokenValid = true

        if (!tokenValid) {
          // Token 刷新失败，用户已被自动登出，不需要额外处理
          return
        }
      } catch (error) {
        console.error('Token 验证失败:', error)
        // Token 验证失败，清除用户状态并重定向到登录页
        // await userStore.logout()
        ElMessage.warning('登录状态异常，请重新登录')
        next({
          path: '/auth/login',
          query: { redirect: to.fullPath },
        })
        return
      }
    }

    // 其他情况正常通过
    next()
  })
}
