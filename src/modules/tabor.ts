import type { UserModule } from '~/types'

export const install: UserModule = ({ router }) => {
  let isFirstNavigation = true
  let pendingRoute: any = null

  // 路由守卫：确保第一个页面固定是 /dashboard
  router.beforeEach((to, _from, next) => {
    // 如果是首次访问且不是访问 /dashboard，并且目标路由使用 oa 布局
    if (isFirstNavigation && to.path !== '/dashboard' && to.meta?.layout === 'oa') {
      isFirstNavigation = false
      pendingRoute = to
      // 先导航到 /dashboard
      next('/dashboard')
    }
    else {
      if (isFirstNavigation) {
        isFirstNavigation = false
      }
      next()
    }
  })

  // 路由完成后处理待跳转的路由和标签页清理
  router.afterEach((to, _from) => {
    if (pendingRoute && to.path === '/dashboard') {
      // 延迟跳转到原本要访问的路由，确保 /dashboard 已经被 tabor 处理
      setTimeout(() => {
        if (pendingRoute && pendingRoute.path !== '/dashboard') {
          router.push(pendingRoute.fullPath)
          pendingRoute = null
        }
      }, 150)
    }
  })
}
