<script setup lang="ts">
import { useUserStore } from '~/stores/useUserStore'

defineOptions({
  name: 'DashboardPage',
})

const userStore = useUserStore()

let userInfo = userStore.getUserInfo()

// 页面标题
useHead({
  title: '首页 - 物流OA系统',
})

// 统计数据
const stats = ref([
  { title: '今日运单', value: 128, icon: 'i-carbon:delivery-truck', color: '#409EFF' },
  { title: '待处理', value: 23, icon: 'i-carbon:time', color: '#E6A23C' },
  { title: '已完成', value: 105, icon: 'i-carbon:checkmark', color: '#67C23A' },
  { title: '异常单', value: 5, icon: 'i-carbon:warning', color: '#F56C6C' },
])

// 快捷操作
const quickActions = ref([
  { title: '新建运单', icon: 'i-carbon:add', path: '/logistics/waybill/create' },
  { title: '库存管理', icon: 'i-carbon:inventory-management', path: '/logistics/inventory' },
  { title: '车辆调度', icon: 'i-carbon:car', path: '/logistics/vehicle' },
  { title: '用户管理', icon: 'i-carbon:user-multiple', path: '/users' },
])

// 处理快捷操作点击
function handleQuickAction(action: any) {
  ElMessage.info(`跳转到：${action.title}`)
  // 这里可以使用 tabor 打开对应页面
  // const tabor = useTabor()
  // tabor.open({ path: action.path })
}

</script>

<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div mb="6">
      <el-card shadow="never" border="none">
        <div flex="~ items-center justify-between">
          <div>
            <h1 text="2xl" font="bold" mb="2">
              欢迎回来，{{ userInfo?.screenName || '用户' }}！
            </h1>
            <p text="gray-600 dark:gray-400">
              今天是 {{ new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long',
              }) }}
            </p>
          </div>
          <div>
            <div class="i-carbon:delivery-truck" style="width: 48px; height: 48px; color: #409EFF;" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div mb="6">
      <div grid="~ cols-1 md:cols-2 lg:cols-4" gap="4">
        <el-card v-for="stat in stats" :key="stat.title" shadow="hover" class="stat-card">
          <div flex="~ items-center">
            <div
              w="12" h="12" rounded="lg" flex="~ items-center justify-center" mr="4"
              :style="{ backgroundColor: `${stat.color}20` }"
            >
              <div
                :class="stat.icon"
                :style="{ width: '24px', height: '24px', color: stat.color }"
              />
            </div>
            <div>
              <p text="sm gray-600 dark:gray-400" mb="1">
                {{ stat.title }}
              </p>
              <p text="2xl" font="bold">
                {{ stat.value }}
              </p>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div mb="6">
      <h2 text="lg" font="semibold" mb="4">
        快捷操作
      </h2>
      <div grid="~ cols-2 md:cols-4" gap="4">
        <el-card
          v-for="action in quickActions"
          :key="action.title"
          shadow="hover"
          class="action-card"
          @click="handleQuickAction(action)"
        >
          <div text="center" cursor="pointer">
            <div mb="3">
              <div
                :class="action.icon"
                style="width: 32px; height: 32px; color: #409EFF; margin: 0 auto;"
              />
            </div>
            <p font="medium">
              {{ action.title }}
            </p>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 最近活动 -->
    <div mb="6">
      <h2 text="lg" font="semibold" mb="4">
        最近活动
      </h2>
      <el-card shadow="hover">
        <el-timeline>
          <el-timeline-item timestamp="2024-01-15 10:30" placement="top">
            <el-card>
              <h4>新建运单 #WB20240115001</h4>
              <p text="sm gray-600 dark:gray-400">
                从上海发往北京的货物运输
              </p>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="2024-01-15 09:15" placement="top">
            <el-card>
              <h4>库存更新</h4>
              <p text="sm gray-600 dark:gray-400">
                仓库A的货物入库完成
              </p>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="2024-01-15 08:45" placement="top">
            <el-card>
              <h4>车辆调度</h4>
              <p text="sm gray-600 dark:gray-400">
                车辆 京A12345 已安排配送任务
              </p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  background-color: var(--el-bg-color-page);
  min-height: 100%;
  padding: 1.5rem;
  padding-bottom: 2rem; /* 确保底部有足够边距 */
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.action-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-card:active {
  transform: translateY(0);
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    hideClose: true
    keepAlive: true
    name: '首页'
</route>
