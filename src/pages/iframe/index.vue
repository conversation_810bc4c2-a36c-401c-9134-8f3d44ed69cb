<script setup lang="ts">
import { Link, Refresh } from '@element-plus/icons-vue'
import { computed, onMounted, ref, watch } from 'vue'

const route = useRoute()

// 从查询参数获取 iframe 配置
const src = computed(() => {
  const srcParam = route.query.src as string
  // 验证src参数是否存在且有效
  if (!srcParam || srcParam.trim() === '') {
    return ''
  }
  return srcParam
})
const title = computed(() => (route.query.title as string) || '外部页面')

// iframe 引用和状态
const iframeRef = ref<HTMLIFrameElement>()
const loading = ref(true)
const error = ref(false)

// iframe 加载完成
function onIframeLoad() {
  loading.value = false
  error.value = false
}

// iframe 加载错误
function onIframeError() {
  loading.value = false
  error.value = true
  ElMessage.error('页面加载失败')
}

// 刷新页面
function refreshPage() {
  if (iframeRef.value && src.value) {
    loading.value = true
    error.value = false
    // 通过重新设置 src 来刷新 iframe
    const currentSrc = iframeRef.value.src
    iframeRef.value.src = ''
    setTimeout(() => {
      if (iframeRef.value) {
        iframeRef.value.src = currentSrc
      }
    }, 100)
  }
}

// 在新窗口打开
function openInNewWindow() {
  if (src.value) {
    window.open(src.value, '_blank')
  }
}

// 监听路由变化，当src参数变化时重新加载iframe
watch(() => route.query.src, (newSrc) => {
  if (newSrc && iframeRef.value) {
    loading.value = true
    error.value = false
  }
}, { immediate: true })

onMounted(() => {
  // 设置页面标题
  if (title.value) {
    document.title = title.value
  }
})
</script>

<template>
  <div class="iframe-container">
    <!-- 工具栏 -->
    <div v-if="src" class="iframe-toolbar">
      <div class="toolbar-left">
        <span class="page-title">{{ title }}</span>
        <el-tag v-if="loading" type="info" size="small">
          加载中...
        </el-tag>
        <el-tag v-else-if="error" type="danger" size="small">
          加载失败
        </el-tag>
        <el-tag v-else type="success" size="small">
          已加载
        </el-tag>
      </div>
      <div class="toolbar-right">
        <el-button size="small" @click="refreshPage">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="openInNewWindow">
          <el-icon><Link /></el-icon>
          新窗口打开
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading && src"
      v-loading="loading"
      class="loading-container"
      element-loading-text="页面加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    />

    <!-- iframe 内容 -->
    <iframe
      v-if="src"
      ref="iframeRef"
      :src="src"
      :title="title"
      class="iframe-content"
      :class="{ 'iframe-loading': loading }"
      frameborder="0"
      allowfullscreen
      sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
      @load="onIframeLoad"
      @error="onIframeError"
    />

    <!-- 错误状态 -->
    <div v-if="!src || error" class="error-container">
      <el-result
        icon="warning"
        :title="error ? '页面加载失败' : '页面地址无效'"
        :sub-title="error ? '请检查网络连接或页面地址是否正确' : '未找到有效的页面地址'"
      >
        <template #extra>
          <el-button v-if="error" type="primary" @click="refreshPage">
            重新加载
          </el-button>
          <el-button @click="$router.go(-1)">
            返回上一页
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<style scoped>
.tabor-pages {
  height: 100%;
}

.iframe-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.iframe-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.iframe-content {
  flex: 1;
  width: 100%;
  border: none;
  display: block;
  background-color: #fff;
}

.iframe-loading {
  opacity: 0.7;
  pointer-events: none;
}

.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    keepAlive: false
    name: '外部页面'
</route>
