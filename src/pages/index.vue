<script setup lang="ts">
defineOptions({
  name: 'IndexPage',
})

const router = useRouter()
onMounted(() => {
  router.push('/dashboard')
})

useHead({
  title: '欢迎',
})
</script>

<template>
  <div>
    <div class="h-screen flex items-center justify-center">
      <el-result icon="success" title="正在跳转到系统首页..." />
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  tabConfig:
    exclude: true
</route>
