<script setup lang="ts">
defineOptions({
  name: 'EventBusDemoPage',
})

// 事件总线
const { $emit, $on, $clear, $getListenerCount, $getEventNames } = useEventBusWithCleanup()

// 响应式数据
const customEventName = ref('custom:test')
const customEventData = ref('{"message": "Hello World"}')
const eventLogs = ref<Array<{ timestamp: string, eventName: string, data: string }>>([])

// 监听器统计
const userLoginListenerCount = ref(0)
const userLogoutListenerCount = ref(0)
const themeChangeListenerCount = ref(0)
const dataRefreshListenerCount = ref(0)
const allEventNames = ref<string[]>([])

// 发送事件方法
function emitUserLogin() {
  $emit('user:login', {
    userId: Math.random().toString(36).substr(2, 9),
    username: 'demo-user',
    timestamp: Date.now(),
  })
}

function emitUserLogout() {
  $emit('user:logout', {
    userId: 'demo-user-id',
    reason: 'manual',
  })
}

function emitThemeChange() {
  const themes = ['light', 'dark']
  const theme = themes[Math.floor(Math.random() * themes.length)]
  $emit('system:theme-changed', { theme })
}

function emitDataRefresh() {
  $emit('business:data-refresh', {
    module: 'orders',
    timestamp: Date.now(),
  })
}

function emitCustomEvent() {
  try {
    const data = customEventData.value ? JSON.parse(customEventData.value) : undefined
    $emit(customEventName.value, data)
  }
  catch (error) {
    ElMessage.error('事件数据格式错误，请输入有效的 JSON')
  }
}

// 工具方法
function clearAllListeners() {
  $clear()
  refreshStats()
  ElMessage.success('已清除所有监听器')
}

function clearLogs() {
  eventLogs.value = []
}

function refreshStats() {
  userLoginListenerCount.value = $getListenerCount('user:login')
  userLogoutListenerCount.value = $getListenerCount('user:logout')
  themeChangeListenerCount.value = $getListenerCount('system:theme-changed')
  dataRefreshListenerCount.value = $getListenerCount('business:data-refresh')
  allEventNames.value = $getEventNames()
}

// 添加事件日志
function addEventLog(eventName: string, data: any) {
  const timestamp = new Date().toLocaleTimeString()
  const dataStr = data ? JSON.stringify(data, null, 2) : 'undefined'
  eventLogs.value.unshift({
    timestamp,
    eventName,
    data: dataStr,
  })

  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 设置事件监听器
onMounted(() => {
  // 监听所有可能的事件
  const eventNames = [
    'user:login',
    'user:logout',
    'system:theme-changed',
    'business:data-refresh',
    customEventName.value,
  ]

  eventNames.forEach((eventName) => {
    $on(eventName, (data) => {
      addEventLog(eventName, data)
    })
  })

  // 监听自定义事件名变化
  watch(() => customEventName.value, (newEventName, oldEventName) => {
    if (newEventName && newEventName !== oldEventName) {
      $on(newEventName, (data) => {
        addEventLog(newEventName, data)
      })
    }
  })

  // 初始化统计
  refreshStats()

  // 定期刷新统计
  const timer = setInterval(refreshStats, 2000)
  onUnmounted(() => clearInterval(timer))
})
</script>

<template>
  <div class="mx-auto max-w-4xl p-6">
    <h1 class="mb-6 text-3xl font-bold">
      全局事件总线演示
    </h1>

    <!-- 发送事件区域 -->
    <div class="mb-8 border rounded-lg p-4">
      <h2 class="mb-4 text-xl font-semibold">
        发送事件
      </h2>

      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <h3 class="mb-2 font-medium">
            用户事件
          </h3>
          <div class="space-y-2">
            <button
              class="w-full btn"
              @click="emitUserLogin"
            >
              发送用户登录事件
            </button>
            <button
              class="w-full btn"
              @click="emitUserLogout"
            >
              发送用户退出事件
            </button>
          </div>
        </div>

        <div>
          <h3 class="mb-2 font-medium">
            系统事件
          </h3>
          <div class="space-y-2">
            <button
              class="w-full btn"
              @click="emitThemeChange"
            >
              切换主题事件
            </button>
            <button
              class="w-full btn"
              @click="emitDataRefresh"
            >
              数据刷新事件
            </button>
          </div>
        </div>
      </div>

      <!-- 自定义事件 -->
      <div class="mt-4">
        <h3 class="mb-2 font-medium">
          自定义事件
        </h3>
        <div class="flex gap-2">
          <input
            v-model="customEventName"
            placeholder="事件名称"
            class="flex-1 border rounded px-3 py-2"
          >
          <input
            v-model="customEventData"
            placeholder="事件数据 (JSON)"
            class="flex-1 border rounded px-3 py-2"
          >
          <button
            class="btn"
            @click="emitCustomEvent"
          >
            发送
          </button>
        </div>
      </div>
    </div>

    <!-- 事件监听区域 -->
    <div class="mb-8 border rounded-lg p-4">
      <h2 class="mb-4 text-xl font-semibold">
        事件监听
      </h2>

      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <h3 class="mb-2 font-medium">
            监听器状态
          </h3>
          <div class="text-sm space-y-2">
            <div>用户登录监听器: {{ userLoginListenerCount }} 个</div>
            <div>用户退出监听器: {{ userLogoutListenerCount }} 个</div>
            <div>主题切换监听器: {{ themeChangeListenerCount }} 个</div>
            <div>数据刷新监听器: {{ dataRefreshListenerCount }} 个</div>
          </div>
        </div>

        <div>
          <h3 class="mb-2 font-medium">
            所有事件
          </h3>
          <div class="text-sm">
            <div>总事件数: {{ allEventNames.length }}</div>
            <div class="max-h-20 overflow-y-auto">
              <div v-for="eventName in allEventNames" :key="eventName" class="text-xs">
                {{ eventName }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <button
          class="mr-2 btn"
          @click="clearAllListeners"
        >
          清除所有监听器
        </button>
        <button
          class="btn"
          @click="refreshStats"
        >
          刷新统计
        </button>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="border rounded-lg p-4">
      <div class="mb-4 flex items-center justify-between">
        <h2 class="text-xl font-semibold">
          事件日志
        </h2>
        <button
          class="btn"
          @click="clearLogs"
        >
          清除日志
        </button>
      </div>

      <div class="max-h-60 overflow-y-auto rounded bg-gray-100 p-4">
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="mb-2 text-sm font-mono"
        >
          <span class="text-gray-500">[{{ log.timestamp }}]</span>
          <span class="text-blue-600 font-medium">{{ log.eventName }}</span>
          <span class="text-gray-700">{{ log.data }}</span>
        </div>
        <div v-if="eventLogs.length === 0" class="text-center text-gray-500">
          暂无事件日志
        </div>
      </div>
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: oa
</route>
