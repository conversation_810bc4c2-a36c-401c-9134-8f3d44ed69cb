<script setup lang="ts">
import { Close, Delete, Link, Plus, Refresh } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useTabor } from 'vue3-tabor'

const tabor = useTabor()
const route = useRoute()

// 测试数据
const testCounter = ref(1)
const externalUrlInput = ref('https://www.baidu.com')
const customTitleInput = ref('百度搜索')

// 获取当前所有标签页（暂未使用）
// const allTabs = computed(() => {
//   // 这里需要从tabor store中获取所有标签页，具体API可能需要调整
//   return []
// })

// 1. 打开测试页面
function openTestPage() {
  const pageNumber = testCounter.value++
  tabor.open({
    path: '/tabor-test/sub',
    query: {
      page: pageNumber,
      title: `测试页面${pageNumber}`,
      timestamp: Date.now(),
    },
  }, {
    tabConfig: {
      name: `测试页面${pageNumber}`,
      keepAlive: true,
      key: 'fullPath',
    },
  })
}

// 2. 打开外部页面
function openExternalPage() {
  if (!externalUrlInput.value) {
    ElMessage.error('请输入外部页面地址')
    return
  }

  try {
    // 验证URL格式
    const url = new URL(externalUrlInput.value)
    // URL验证通过
    void url
  }
  catch {
    ElMessage.error('请输入有效的URL地址')
    return
  }

  const timestamp = Date.now()
  tabor.open({
    path: '/iframe',
    query: {
      src: externalUrlInput.value,
      title: customTitleInput.value || '外部页面',
      t: timestamp,
    },
  }, {
    tabConfig: {
      name: customTitleInput.value || '外部页面',
      keepAlive: false,
      key: 'fullPath',
      iframeAttributes: {
        src: externalUrlInput.value,
        width: '100%',
        height: '100%',
      },
    },
  })
}

// 3. 打开特殊配置的页面
function openSpecialPage() {
  tabor.open({
    path: '/tabor-test/special',
    query: {
      special: 'true',
      timestamp: Date.now(),
    },
  }, {
    tabConfig: {
      name: '特殊页面(不可关闭)',
      keepAlive: true,
      hideClose: true,
      key: 'fullPath',
    },
  })
}

// 4. 打开网络缓慢测试页面
function openLoadingTestPage() {
  tabor.open({
    path: '/tabor-test/slow-loading',
    query: {
      test: 'slow-network',
      timestamp: Date.now()
    }
  }, {
    tabConfig: {
      name: '网络缓慢测试',
      keepAlive: false,
      key: 'fullPath'
    }
  })
}

// 5. 关闭当前页面后打开新页面
function closeCurrentAndOpenNew() {
  ElMessage.info('正在关闭当前页面并打开新页面...')

  // 获取当前页面路径
  const currentPath = route.fullPath

  // 先关闭当前页面
  tabor.close({ fullPath: currentPath })

  // 延迟打开新页面，确保当前页面已经关闭
  setTimeout(() => {
    tabor.open({
      path: '/tabor-test/sub',
      query: {
        page: 'after-close',
        title: '关闭后新页面',
        timestamp: Date.now(),
        source: 'after-close'
      }
    }, {
      tabConfig: {
        name: '关闭后新页面',
        keepAlive: true,
        key: 'fullPath'
      }
    })
  }, 300)
}

// 5. 刷新指定页面
function refreshPage(path?: string) {
  if (path) {
    // 刷新指定路径的页面
    tabor.refresh(path)
    ElMessage.success(`页面 ${path} 刷新成功`)
  }
  else {
    // 刷新当前页面 - 使用当前路由的fullPath作为标识
    const currentPath = route.fullPath
    tabor.refresh(currentPath)
    ElMessage.success('当前页面刷新成功')
  }
}

// 6. 关闭指定页面
function closePage(path?: string) {
  if (path) {
    tabor.close({ fullPath: path })
  }
  else {
    tabor.close()
  }
  ElMessage.success('页面关闭成功')
}

// 7. 关闭其他页面
function closeOtherPages() {
  tabor.closeOthers()
  ElMessage.success('其他页面已关闭')
}

// 8. 查找页面
function findPage() {
  const currentPath = '/tabor-test'
  const tab = tabor.find(currentPath)
  if (tab) {
    ElMessage.success(`找到页面: ${String(tab.name)}`)
  }
  else {
    ElMessage.warning('未找到指定页面')
  }
}

// 9. 检查页面是否存在
function checkPageExists() {
  const exists = tabor.has('/dashboard')
  ElMessage.info(`首页是否存在: ${exists ? '是' : '否'}`)
}

// 预定义的测试页面列表
const testPages = ref([
  { path: '/dashboard', name: '首页' },
  { path: '/users', name: '用户管理' },
  { path: '/tabor-test', name: '当前测试页' },
])

// 预定义的外部页面列表
const externalPages = ref([
  { url: 'https://www.baidu.com', title: '百度搜索' },
  { url: 'https://element-plus.org/zh-CN/', title: 'Element Plus' },
  { url: 'https://cn.vuejs.org/', title: 'Vue.js' },
  { url: 'https://github.com', title: 'GitHub' },
])

// 快速打开预定义外部页面
function openPredefinedExternal(item: { url: string, title: string }) {
  externalUrlInput.value = item.url
  customTitleInput.value = item.title
  openExternalPage()
}
</script>

<template>
  <div class="tabor-test-container">
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span class="text-lg font-bold">Vue-Tabor 功能测试页面</span>
        </div>
      </template>

      <div class="test-sections">
        <!-- 基础页面操作 -->
        <el-card class="mb-4" shadow="hover">
          <template #header>
            <span class="font-semibold">基础页面操作</span>
          </template>

          <div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
            <el-button type="primary" :icon="Plus" @click="openTestPage">
              打开测试页面
            </el-button>

            <el-button type="success" :icon="Plus" @click="openSpecialPage">
              打开特殊页面
            </el-button>

            <el-button type="warning" :icon="Plus" @click="openLoadingTestPage">
              网络缓慢测试
            </el-button>

            <el-button type="danger" :icon="Close" @click="closeCurrentAndOpenNew">
              关闭后打开新页面
            </el-button>

            <el-button type="info" :icon="Refresh" @click="refreshPage()">
              刷新当前页面
            </el-button>
          </div>
        </el-card>

        <!-- 外部页面操作 -->
        <el-card class="mb-4" shadow="hover">
          <template #header>
            <span class="font-semibold">外部页面操作</span>
          </template>

          <div class="space-y-4">
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
              <el-input
                v-model="externalUrlInput"
                placeholder="输入外部页面URL"
                clearable
              />
              <el-input
                v-model="customTitleInput"
                placeholder="自定义标题"
                clearable
              />
              <el-button type="primary" :icon="Link" @click="openExternalPage">
                打开外部页面
              </el-button>
            </div>

            <div>
              <p class="mb-2 text-sm text-gray-600">
                快速打开预定义外部页面：
              </p>
              <div class="grid grid-cols-2 gap-2 md:grid-cols-4">
                <el-button
                  v-for="item in externalPages"
                  :key="item.url"
                  size="small"
                  @click="openPredefinedExternal(item)"
                >
                  {{ item.title }}
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 页面管理操作 -->
        <el-card class="mb-4" shadow="hover">
          <template #header>
            <span class="font-semibold">页面管理操作</span>
          </template>

          <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
            <el-button type="danger" :icon="Close" @click="closePage()">
              关闭当前页面
            </el-button>

            <el-button type="danger" :icon="Delete" @click="closeOtherPages">
              关闭其他页面
            </el-button>

            <el-button type="info" @click="findPage">
              查找页面
            </el-button>

            <el-button type="info" @click="checkPageExists">
              检查页面存在
            </el-button>
          </div>
        </el-card>

        <!-- 页面列表操作 -->
        <el-card class="mb-4" shadow="hover">
          <template #header>
            <span class="font-semibold">页面列表操作</span>
          </template>

          <div class="space-y-4">
            <div>
              <p class="mb-2 text-sm text-gray-600">
                对指定页面进行操作：
              </p>
              <div class="space-y-2">
                <div
                  v-for="page in testPages"
                  :key="page.path"
                  class="flex items-center justify-between border rounded p-3"
                >
                  <span class="font-medium">{{ page.name }} ({{ page.path }})</span>
                  <div class="space-x-2">
                    <el-button size="small" type="primary" @click="refreshPage(page.path)">
                      刷新
                    </el-button>
                    <el-button size="small" type="danger" @click="closePage(page.path)">
                      关闭
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 测试说明 -->
        <el-card shadow="hover">
          <template #header>
            <span class="font-semibold">测试说明</span>
          </template>

          <div class="text-sm text-gray-600 space-y-2">
            <p><strong>基础页面操作：</strong></p>
            <ul class="ml-4 list-disc list-inside space-y-1">
              <li>打开测试页面：创建新的内部测试页面，支持缓存</li>
              <li>打开特殊页面：创建不可关闭的页面</li>
              <li>网络缓慢测试：模拟网络缓慢，10秒加载时间</li>
              <li>关闭后打开新页面：测试关闭当前页面后自动打开新页面</li>
              <li>刷新当前页面：重新加载当前页面内容</li>
            </ul>

            <p><strong>外部页面操作：</strong></p>
            <ul class="ml-4 list-disc list-inside space-y-1">
              <li>支持自定义URL和标题</li>
              <li>每次打开都会创建新的iframe标签页</li>
              <li>提供常用网站快速访问</li>
            </ul>

            <p><strong>页面管理操作：</strong></p>
            <ul class="ml-4 list-disc list-inside space-y-1">
              <li>关闭当前/指定页面</li>
              <li>关闭除当前页面外的所有页面</li>
              <li>查找和检查页面是否存在</li>
            </ul>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.tabor-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-sections {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    name: 'Tabor测试'
    keepAlive: true
</route>
