<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useTabor } from 'vue3-tabor'

const tabor = useTabor()

// 简单的加载状态
const loading = ref(true)
const loadStartTime = ref(Date.now())

// 页面数据
const pageData = ref({
  title: '网络缓慢加载测试页面',
  description: '这是一个模拟网络缓慢的测试页面，用于测试vue-tabor在慢速加载时的表现。',
  loadTime: '0秒',
  networkSpeed: '慢速网络 (10秒延迟)',
  features: [
    '模拟网络延迟',
    '测试加载状态',
    '验证用户体验',
    '检查标签页行为'
  ]
})

onMounted(() => {
  // 模拟网络缓慢，10秒后加载完成
  setTimeout(() => {
    loading.value = false
    const actualLoadTime = ((Date.now() - loadStartTime.value) / 1000).toFixed(1)
    pageData.value.loadTime = `${actualLoadTime}秒`
    ElMessage.success('页面加载完成！')
  }, 10000) // 10秒延迟
})

// 重新加载
function reloadPage() {
  loading.value = true
  loadStartTime.value = Date.now()
  
  setTimeout(() => {
    loading.value = false
    const actualLoadTime = ((Date.now() - loadStartTime.value) / 1000).toFixed(1)
    pageData.value.loadTime = `${actualLoadTime}秒`
    ElMessage.success('页面重新加载完成！')
  }, 10000)
}

// 返回主测试页
function backToMain() {
  const existingTab = tabor.find('/tabor-test')
  if (existingTab) {
    tabor.setActive(existingTab)
  } else {
    tabor.open({ path: '/tabor-test' })
  }
}

// 关闭当前页面
function closeSelf() {
  tabor.close()
}
</script>

<template>
  <div class="slow-loading-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-card class="loading-card">
        <div class="loading-content">
          <!-- 简单的加载动画 -->
          <div class="loading-spinner">
            <el-icon class="rotating" size="64">
              <Loading />
            </el-icon>
          </div>
          
          <!-- 加载文本 -->
          <h2 class="loading-title">正在加载页面...</h2>
          <p class="loading-subtitle">模拟网络缓慢，请耐心等待</p>
          
          <!-- 简单的进度指示 -->
          <div class="loading-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 加载完成后的内容 -->
    <div v-else class="loaded-content">
      <el-card class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="text-lg font-bold">{{ pageData.title }}</span>
            <div class="space-x-2">
              <el-button size="small" @click="backToMain">返回主页</el-button>
              <el-button size="small" type="primary" @click="reloadPage">重新加载</el-button>
              <el-button size="small" type="danger" @click="closeSelf">关闭</el-button>
            </div>
          </div>
        </template>
        
        <div class="space-y-4">
          <el-alert
            title="网络缓慢加载测试完成"
            type="success"
            :closable="false"
          >
            <p>{{ pageData.description }}</p>
          </el-alert>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 加载统计 -->
            <el-card shadow="hover">
              <template #header>加载信息</template>
              <div class="space-y-2 text-sm">
                <p><strong>实际加载时间:</strong> {{ pageData.loadTime }}</p>
                <p><strong>网络状况:</strong> {{ pageData.networkSpeed }}</p>
                <p><strong>开始时间:</strong> {{ new Date(loadStartTime).toLocaleTimeString() }}</p>
                <p><strong>页面状态:</strong> 加载完成</p>
              </div>
            </el-card>
            
            <!-- 测试特性 -->
            <el-card shadow="hover">
              <template #header>测试特性</template>
              <ul class="space-y-1 text-sm">
                <li v-for="feature in pageData.features" :key="feature" class="flex items-center">
                  <el-icon class="mr-2 text-green-500"><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </el-card>
          </div>
        </div>
      </el-card>

      <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded text-sm">
        <p><strong>测试说明:</strong></p>
        <ul class="list-disc list-inside mt-2 space-y-1">
          <li>这个页面模拟了网络缓慢的情况，加载需要10秒</li>
          <li>可以测试vue-tabor在长时间加载时的表现</li>
          <li>观察标签页的加载状态和用户体验</li>
          <li>可以重新加载来重复测试网络缓慢的情况</li>
          <li>测试用户在等待过程中的耐心和体验</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped>
.slow-loading-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  min-height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.loading-card {
  width: 100%;
  max-width: 500px;
}

.loading-content {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  margin-bottom: 30px;
}

.rotating {
  animation: rotate 2s linear infinite;
  color: #409eff;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-title {
  margin: 20px 0 10px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.loading-subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 30px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409eff;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.5s;
}

.dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.loaded-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    name: '网络缓慢测试'
    keepAlive: false
</route>
