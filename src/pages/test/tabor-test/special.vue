<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useTabor } from 'vue3-tabor'

const tabor = useTabor()

// 页面状态
const message = ref('这是一个特殊的测试页面')
const logs = ref<string[]>([])
const autoRefreshEnabled = ref(false)
const refreshInterval = ref<NodeJS.Timeout>()

onMounted(() => {
  addLog('页面已加载')
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 添加日志
function addLog(text: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${text}`)
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 清空日志
function clearLogs() {
  logs.value = []
  addLog('日志已清空')
}

// 测试消息
function showMessage() {
  ElMessage.success('这是一个测试消息')
  addLog('显示了测试消息')
}

// 测试通知
function showNotification() {
  // 使用ElMessage代替ElNotification，因为自动导入可能不包含ElNotification
  ElMessage({
    message: '这是一个来自特殊页面的通知',
    type: 'info',
    duration: 3000,
  })
  addLog('显示了测试通知')
}

// 测试确认对话框
function showConfirm() {
  // 简化为普通消息提示
  ElMessage({
    message: '这是一个测试确认功能（简化版）',
    type: 'warning',
    duration: 3000,
  })
  addLog('显示了确认测试消息')
}

// 切换自动刷新
function toggleAutoRefresh() {
  if (autoRefreshEnabled.value) {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
    autoRefreshEnabled.value = false
    addLog('自动刷新已停止')
  }
  else {
    refreshInterval.value = setInterval(() => {
      addLog('自动刷新触发')
    }, 5000)
    autoRefreshEnabled.value = true
    addLog('自动刷新已启动（每5秒）')
  }
}

// 返回主测试页
function backToMain() {
  // 检查主测试页是否已经存在
  const existingTab = tabor.find('/tabor-test')
  if (existingTab) {
    // 如果存在，直接切换到该标签页
    tabor.setActive(existingTab)
  } else {
    // 如果不存在，才打开新的标签页
    tabor.open({
      path: '/tabor-test',
    })
  }
}

// 尝试关闭页面（应该失败，因为设置了hideClose）
function tryClose() {
  try {
    tabor.close()
    addLog('尝试关闭页面')
  }
  catch (error) {
    addLog('关闭页面失败（预期行为）')
  }
}

// 模拟长时间运行的任务
const taskRunning = ref(false)
function runLongTask() {
  if (taskRunning.value)
    return

  taskRunning.value = true
  addLog('开始执行长时间任务...')

  let progress = 0
  const taskInterval = setInterval(() => {
    progress += 10
    addLog(`任务进度: ${progress}%`)

    if (progress >= 100) {
      clearInterval(taskInterval)
      taskRunning.value = false
      addLog('长时间任务完成')
      ElMessage.success('任务执行完成')
    }
  }, 500)
}
</script>

<template>
  <div class="special-page">
    <el-card class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-bold">特殊测试页面</span>
          <div class="space-x-2">
            <el-tag type="warning">
              不可关闭
            </el-tag>
            <el-button size="small" @click="backToMain">
              返回主页
            </el-button>
          </div>
        </div>
      </template>

      <el-alert
        title="页面特性说明"
        type="info"
        :closable="false"
        class="mb-4"
      >
        <p>这个页面具有以下特殊配置：</p>
        <ul class="mt-2 list-disc list-inside">
          <li><strong>hideClose: true</strong> - 隐藏关闭按钮，页面不可通过标签页关闭</li>
          <li><strong>keepAlive: true</strong> - 启用页面缓存，状态会保持</li>
          <li>适用于重要的系统页面或长时间运行的任务页面</li>
        </ul>
      </el-alert>
    </el-card>

    <div class="grid grid-cols-1 mb-4 gap-4 md:grid-cols-2">
      <!-- 页面状态 -->
      <el-card shadow="hover">
        <template #header>
          页面状态控制
        </template>
        <div class="space-y-3">
          <el-input v-model="message" placeholder="编辑消息内容" />
          <div class="flex items-center space-x-2">
            <span>自动刷新:</span>
            <el-switch
              v-model="autoRefreshEnabled"
              @change="toggleAutoRefresh"
            />
            <span class="text-sm text-gray-500">
              {{ autoRefreshEnabled ? '已启用' : '已禁用' }}
            </span>
          </div>
        </div>
      </el-card>

      <!-- 交互测试 -->
      <el-card shadow="hover">
        <template #header>
          交互功能测试
        </template>
        <div class="grid grid-cols-2 gap-2">
          <el-button size="small" @click="showMessage">
            测试消息
          </el-button>
          <el-button size="small" @click="showNotification">
            测试通知
          </el-button>
          <el-button size="small" @click="showConfirm">
            测试确认框
          </el-button>
          <el-button size="small" @click="tryClose">
            尝试关闭
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 长时间任务测试 -->
    <el-card class="mb-4">
      <template #header>
        长时间任务测试
      </template>
      <div class="space-y-3">
        <p class="text-sm text-gray-600">
          测试页面在执行长时间任务时的状态保持能力
        </p>
        <el-button
          type="primary"
          :loading="taskRunning"
          :disabled="taskRunning"
          @click="runLongTask"
        >
          {{ taskRunning ? '任务执行中...' : '开始长时间任务' }}
        </el-button>
      </div>
    </el-card>

    <!-- 日志显示 -->
    <el-card>
      <template #header>
        <div class="flex items-center justify-between">
          <span>操作日志</span>
          <el-button size="small" @click="clearLogs">
            清空日志
          </el-button>
        </div>
      </template>

      <div class="log-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item"
          :class="{ 'log-new': index === 0 }"
        >
          {{ log }}
        </div>
        <div v-if="logs.length === 0" class="py-4 text-center text-gray-500">
          暂无日志记录
        </div>
      </div>
    </el-card>

    <div class="mt-4 border border-yellow-200 rounded bg-yellow-50 p-4 text-sm">
      <p><strong>测试要点:</strong></p>
      <ul class="mt-2 list-disc list-inside space-y-1">
        <li>尝试点击标签页的关闭按钮 - 应该没有关闭按钮</li>
        <li>切换到其他页面再回来 - 页面状态应该保持</li>
        <li>在任务运行时切换页面 - 任务应该继续在后台执行</li>
        <li>启用自动刷新后切换页面 - 回来时自动刷新应该仍在运行</li>
        <li>只能通过"关闭其他页面"功能或程序化方式关闭此页面</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.special-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.log-item {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-new {
  background-color: #e8f5e8;
  animation: highlight 1s ease-out;
}

@keyframes highlight {
  from {
    background-color: #c8e6c9;
  }
  to {
    background-color: #e8f5e8;
  }
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    name: '特殊页面(不可关闭)'
    keepAlive: true
    hideClose: true
    key: 'fullPath'
</route>
