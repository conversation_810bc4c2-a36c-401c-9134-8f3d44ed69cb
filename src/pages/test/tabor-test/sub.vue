<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTabor } from 'vue3-tabor'

const route = useRoute()
const tabor = useTabor()

// 从路由参数获取页面信息
const pageNumber = computed(() => route.query.page || '1')
const pageTitle = computed(() => route.query.title || '测试页面')
const timestamp = computed(() => route.query.timestamp || Date.now())

// 页面状态
const counter = ref(0)
const inputValue = ref('')
const currentTime = ref(new Date().toLocaleString())

// 定时更新时间
let timeInterval: NodeJS.Timeout

onMounted(() => {
  timeInterval = setInterval(() => {
    currentTime.value = new Date().toLocaleString()
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})

// 增加计数器
function increment() {
  counter.value++
}

// 减少计数器
function decrement() {
  counter.value--
}

// 重置计数器
function reset() {
  counter.value = 0
  inputValue.value = ''
  ElMessage.success('页面状态已重置')
}

// 打开新的测试页面
function openNewTestPage() {
  const newPageNumber = parseInt(pageNumber.value as string) + 1
  tabor.open({
    path: '/tabor-test/sub',
    query: {
      page: newPageNumber,
      title: `测试页面${newPageNumber}`,
      timestamp: Date.now()
    }
  }, {
    tabConfig: {
      name: `测试页面${newPageNumber}`,
      keepAlive: true,
      key: 'fullPath'
    }
  })
}

// 返回主测试页
function backToMain() {
  // 检查主测试页是否已经存在
  const existingTab = tabor.find('/tabor-test')
  if (existingTab) {
    // 如果存在，直接切换到该标签页
    tabor.setActive(existingTab)
  } else {
    // 如果不存在，才打开新的标签页
    tabor.open({
      path: '/tabor-test'
    })
  }
}

// 关闭当前页面
function closeSelf() {
  tabor.close()
}

// 刷新当前页面
function refreshSelf() {
  tabor.refresh()
}

// 模拟数据加载
const loading = ref(false)
const tableData = ref([
  { id: 1, name: '张三', age: 25, city: '北京' },
  { id: 2, name: '李四', age: 30, city: '上海' },
  { id: 3, name: '王五', age: 28, city: '广州' },
])

function loadData() {
  loading.value = true
  setTimeout(() => {
    tableData.value.push({
      id: tableData.value.length + 1,
      name: `用户${tableData.value.length + 1}`,
      age: Math.floor(Math.random() * 40) + 20,
      city: ['北京', '上海', '广州', '深圳'][Math.floor(Math.random() * 4)]
    })
    loading.value = false
    ElMessage.success('数据加载成功')
  }, 1000)
}
</script>

<template>
  <div class="test-sub-page">
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-bold">{{ pageTitle }}</span>
          <div class="space-x-2">
            <el-button size="small" @click="backToMain">返回主页</el-button>
            <el-button size="small" type="primary" @click="refreshSelf">刷新</el-button>
            <el-button size="small" type="danger" @click="closeSelf">关闭</el-button>
          </div>
        </div>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <el-card shadow="hover">
          <template #header>页面信息</template>
          <div class="space-y-2 text-sm">
            <p><strong>页面编号:</strong> {{ pageNumber }}</p>
            <p><strong>创建时间:</strong> {{ new Date(Number(timestamp)).toLocaleString() }}</p>
            <p><strong>当前时间:</strong> {{ currentTime }}</p>
            <p><strong>路由路径:</strong> {{ route.fullPath }}</p>
          </div>
        </el-card>
        
        <el-card shadow="hover">
          <template #header>页面状态</template>
          <div class="space-y-3">
            <div class="flex items-center space-x-2">
              <span>计数器:</span>
              <el-button size="small" @click="decrement">-</el-button>
              <span class="font-bold text-lg">{{ counter }}</span>
              <el-button size="small" @click="increment">+</el-button>
            </div>
            <el-input v-model="inputValue" placeholder="输入一些文本测试状态保持" />
            <el-button size="small" type="warning" @click="reset">重置状态</el-button>
          </div>
        </el-card>
      </div>
    </el-card>

    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span>数据表格测试</span>
          <el-button type="primary" :loading="loading" @click="loadData">
            加载更多数据
          </el-button>
        </div>
      </template>
      
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="age" label="年龄" />
        <el-table-column prop="city" label="城市" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button size="small" @click="ElMessage.info(`查看用户: ${scope.row.name}`)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card>
      <template #header>页面操作测试</template>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <el-button type="primary" @click="openNewTestPage">
          打开新测试页
        </el-button>
        
        <el-button type="success" @click="backToMain">
          返回主测试页
        </el-button>
        
        <el-button type="warning" @click="refreshSelf">
          刷新当前页
        </el-button>
        
        <el-button type="danger" @click="closeSelf">
          关闭当前页
        </el-button>
      </div>
    </el-card>

    <div class="mt-4 p-4 bg-gray-50 rounded text-sm text-gray-600">
      <p><strong>测试说明:</strong></p>
      <ul class="list-disc list-inside mt-2 space-y-1">
        <li>这是一个测试子页面，用于验证页面状态保持功能</li>
        <li>计数器和输入框的值会在页面切换时保持（因为启用了keepAlive）</li>
        <li>可以打开多个相同的测试页面，每个都有独立的状态</li>
        <li>时间会实时更新，验证页面的活跃状态</li>
        <li>表格数据可以动态加载，测试数据状态保持</li>
      </ul>
    </div>
  </div>
</template>

<style scoped>
.test-sub-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}
</style>

<route lang="yaml">
meta:
  layout: oa
  tabConfig:
    name: '测试子页面'
    keepAlive: true
    key: 'fullPath'
</route>
