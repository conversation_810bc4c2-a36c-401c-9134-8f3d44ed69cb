<script setup lang="ts">
import { Link, Lock, User } from '@element-plus/icons-vue'

defineOptions({
  name: 'AuthLoginPage',
})

// 路由
const router = useRouter()
// 路由参数
const route = useRoute()

// 使用  users-auth-func.ts
const UseUsers = new Users()

// 表单数据
const loginForm = reactive({
  username: 'admin',
  password: 'admin',
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 4, max: 20, message: '密码长度在 4 到 20 个字符', trigger: 'blur' },
  ],
}

// 登录状态
const loading = ref(false)
const formRef = ref()
const errorMsg = ref<{
  show: boolean
  message: string
}>({
  show: false,
  message: '',
})

// 登录处理
async function handleLogin() {
  if (!formRef.value)
    return

  try {
    // 验证表单
    await formRef.value.validate()

    loading.value = true

    // 调用登录API并保存登录信息到store
    const r = await UseUsers.useLogin({
      username: loginForm.username,
      password: loginForm.password,
    })

    // console.log('登录结果:', r)

    if (r.code === 0) {
      // error
      errorMsg.value = {
        show: true,
        message: r.message as string,
      }

      return
    }

    // 登录成功
    errorMsg.value = {
      show: false,
      message: '',
    }

    ElMessage.success('登录成功')

    // 获取重定向路径，如果没有则跳转到首页
    const redirectPath = (route.query.redirect as string) || '/dashboard'

    // 跳转到目标页面
    await router.push(redirectPath)
  }
  catch (error: any) {
    console.error('Login error:', error)

    errorMsg.value = {
      show: true,
      message: error.message || '登录验证失败，请检查用户名和密码',
    }
  }
  finally {
    loading.value = false
  }
}

// 键盘事件处理
function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleLogin()
  }
}

// 页面标题
useHead({
  title: '用户登录',
})
</script>

<template>
  <div
    class="relative min-h-screen flex items-center justify-center overflow-hidden from-[#667eea] to-[#764ba2] bg-gradient-to-br"
  >
    <div class="relative z-10 w-400px rounded-4 bg-white/95 p-10 shadow-lg backdrop-blur dark:bg-gray-900/95">
      <!-- 系统标题 -->
      <div class="mb-8 text-center">
        <div class="mb-4">
          <div class="i-carbon:ibm-elo-engineering-insights mx-auto h-12 w-12 text-[#409EFF]" />
        </div>
        <h1 class="m-0 mb-2 text-7 text-gray-800 font-600 dark:text-gray-100">
          登录
        </h1>
        <p class="m-0 text-sm text-gray-600 dark:text-gray-400">
          Login
        </p>
      </div>

      <!-- 第三方登录 -->
      <el-button
        size="large" plain class="h-12 w-full rounded-2 text-4 font-500" :icon="Link" :loading="loading"
        @click="handleLogin"
      >
        企业微信登录
      </el-button>

      <hr class="my-5">

      <!-- 登录表单 -->
      <div v-if="errorMsg.show" class="mb-5">
        <el-alert :closable="false" :title="errorMsg.message" type="error" show-icon />
      </div>

      <el-form ref="formRef" :model="loginForm" :rules="rules" class="mb-6" size="large" @keypress="handleKeyPress">
        <el-form-item prop="username" class="mb-5">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" :prefix-icon="markRaw(User)" clearable />
        </el-form-item>

        <el-form-item prop="password" class="mb-5">
          <el-input
            v-model="loginForm.password" type="password" placeholder="请输入密码" :prefix-icon="markRaw(Lock)"
            show-password clearable
          />
        </el-form-item>

        <el-form-item>
          <el-button
            size="large" type="primary" class="h-12 w-full rounded-2 text-4 font-500" :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 登录提示 -->
      <div class="mt-5">
        <el-alert title="默认账号信息" type="info" :closable="false" show-icon class="rounded-2">
          <template #default>
            <p class="my-1 text-sm">
              用户名：admin
            </p>
            <p class="my-1 text-sm">
              密码：admin
            </p>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="pointer-events-none absolute inset-0 z-1">
      <div class="animate-float absolute left-10% top-10% h-30 w-30 rounded-full bg-white/10" />
      <div class="animate-float absolute right-15% top-70% h-20 w-20 rounded-full bg-white/10 delay-2s" />
      <div class="animate-float absolute bottom-20% left-20% h-15 w-15 rounded-full bg-white/10 delay-4s" />
    </div>
  </div>
</template>

<route lang="yaml">
meta:
  layout: login
  tabConfig:
    exclude: true
</route>
