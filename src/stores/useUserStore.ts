
import { defineStore } from 'pinia'
import type { IUserInfo } from '~/types/users'

export const useUserStore = defineStore('user', {
    state: () => ({
        token: localStorage.getItem('token') || '', // JWT 令牌
        isLoggedIn: false,                          // 登录状态标记
        loading: false,                             // 登录请求加载状态
        error: null as string | null,                // 登录错误信息
        userInfo: null as IUserInfo | null, // 用户信息
    }),
    getters: {
        // 检查是否已登录
        hasToken: (state) => !!state.token
    },
    actions: {
        // 设置登录状态
        setLoggedIn(token: string) {
            this.token = token
            this.isLoggedIn = true
            localStorage.setItem('token', token) // 持久化存储 token
        },
        // 清除登录状态
        logout() {
            this.token = ''
            this.isLoggedIn = false
            localStorage.removeItem('token')
        },
        // 获取用户信息 输出用户信息
        getUserInfo() {
            return this.userInfo
        }
    }
})