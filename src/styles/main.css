@import './markdown.css';

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

html.dark {
  background: #121212;
  color-scheme: dark;
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: rgb(13, 148, 136);
  opacity: 0.75;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
}



/* 布局高度 */
.rt-container{
  height: 100%;
  overflow: hidden;
}
/* 上一个布局也是 100% */
.rt-container .el-container{
  height: 100%;
}
